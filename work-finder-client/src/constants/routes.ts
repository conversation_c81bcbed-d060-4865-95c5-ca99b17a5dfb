// Router constants following the pattern from vsii-internal-fe
export const ROUTES = {
  // Public routes
  HOME: '/',
  
  // Authentication routes
  AUTH: {
    LOGIN: '/auth/login',
    REGISTER: '/auth/register',
    FORGOT_PASSWORD: '/auth/forgot-password',
    RESET_PASSWORD: '/auth/reset-password',
    VERIFY_EMAIL: '/auth/verify-email',
  },

  // Job routes
  JOBS: {
    LIST: '/jobs',
    SEARCH: '/jobs/search',
    DETAIL: (id: string) => `/jobs/${id}`,
    SAVED: '/jobs/saved',
    APPLICATIONS: '/jobs/applications',
  },

  // Company routes
  COMPANIES: {
    LIST: '/companies',
    SEARCH: '/companies/search',
    DETAIL: (id: string) => `/companies/${id}`,
    FOLLOWED: '/companies/followed',
  },

  // User/Profile routes
  PROFILE: {
    INDEX: '/profile',
    EDIT: '/profile/edit',
    SETTINGS: '/profile/settings',
    CHANGE_PASSWORD: '/profile/change-password',
    APPLICATIONS: '/profile/applications',
    SAVED_JOBS: '/profile/saved-jobs',
    FOLLOWED_COMPANIES: '/profile/followed-companies',
  },

  // Dashboard routes (if user has different roles)
  DASHBOARD: {
    INDEX: '/dashboard',
    ANALYTICS: '/dashboard/analytics',
  },

  // Employer routes (if supporting employer features)
  EMPLOYER: {
    INDEX: '/employer',
    JOBS: {
      INDEX: '/employer/jobs',
      CREATE: '/employer/jobs/create',
      EDIT: (id: string) => `/employer/jobs/${id}/edit`,
      APPLICATIONS: (id: string) => `/employer/jobs/${id}/applications`,
    },
    COMPANY: {
      INDEX: '/employer/company',
      EDIT: '/employer/company/edit',
    },
  },

  // Admin routes (if supporting admin features)
  ADMIN: {
    INDEX: '/admin',
    USERS: '/admin/users',
    JOBS: '/admin/jobs',
    COMPANIES: '/admin/companies',
    STATISTICS: '/admin/statistics',
  },

  // Static pages
  ABOUT: '/about',
  CONTACT: '/contact',
  PRIVACY: '/privacy',
  TERMS: '/terms',
  FAQ: '/faq',

  // Error pages
  NOT_FOUND: '/404',
  UNAUTHORIZED: '/401',
  SERVER_ERROR: '/500',
} as const;

// Helper function to check if a route is public (doesn't require authentication)
export const isPublicRoute = (pathname: string): boolean => {
  const publicRoutes = [
    ROUTES.HOME,
    ROUTES.AUTH.LOGIN,
    ROUTES.AUTH.REGISTER,
    ROUTES.AUTH.FORGOT_PASSWORD,
    ROUTES.AUTH.RESET_PASSWORD,
    ROUTES.AUTH.VERIFY_EMAIL,
    ROUTES.JOBS.LIST,
    ROUTES.JOBS.SEARCH,
    ROUTES.COMPANIES.LIST,
    ROUTES.COMPANIES.SEARCH,
    ROUTES.ABOUT,
    ROUTES.CONTACT,
    ROUTES.PRIVACY,
    ROUTES.TERMS,
    ROUTES.FAQ,
    ROUTES.NOT_FOUND,
    ROUTES.UNAUTHORIZED,
    ROUTES.SERVER_ERROR,
  ];

  // Check exact matches
  if (publicRoutes.includes(pathname)) {
    return true;
  }

  // Check dynamic routes (job details, company details)
  if (pathname.startsWith('/jobs/') && pathname.split('/').length === 3) {
    return true; // Job detail pages are public
  }

  if (pathname.startsWith('/companies/') && pathname.split('/').length === 3) {
    return true; // Company detail pages are public
  }

  return false;
};

// Helper function to check if a route requires authentication
export const isProtectedRoute = (pathname: string): boolean => {
  return !isPublicRoute(pathname);
};

// Helper function to get the redirect path after login
export const getRedirectPath = (intendedPath?: string): string => {
  if (!intendedPath || isPublicRoute(intendedPath)) {
    return ROUTES.DASHBOARD.INDEX;
  }
  return intendedPath;
};
