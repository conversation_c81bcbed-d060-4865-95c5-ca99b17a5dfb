import { apiClient } from '@/api/client'
import { Task, TaskFilters, CreateTaskData, UpdateTaskData } from './types'

export interface TasksResponse {
  tasks: Task[]
  total: number
  page: number
  limit: number
  totalPages: number
}

export interface TaskSearchParams extends TaskFilters {
  page?: number
  limit?: number
  sortBy?: string
  sortOrder?: 'ASC' | 'DESC'
}

export const tasksApi = {
  // Get all tasks with filtering and pagination
  async getTasks(params?: TaskSearchParams): Promise<TasksResponse> {
    const response = await apiClient.get('/tasks', { params })
    return response.data
  },

  // Get a specific task by ID
  async getTask(id: string): Promise<Task> {
    const response = await apiClient.get(`/tasks/${id}`)
    return response.data
  },

  // Create a new task
  async createTask(data: CreateTaskData): Promise<Task> {
    const response = await apiClient.post('/tasks', data)
    return response.data
  },

  // Update an existing task
  async updateTask(data: UpdateTaskData): Promise<Task> {
    const { id, ...updateData } = data
    const response = await apiClient.patch(`/tasks/${id}`, updateData)
    return response.data
  },

  // Delete a task
  async deleteTask(id: string): Promise<void> {
    await apiClient.delete(`/tasks/${id}`)
  },

  // Search tasks
  async searchTasks(query: string, filters?: TaskFilters): Promise<TasksResponse> {
    const params = { search: query, ...filters }
    const response = await apiClient.get('/tasks/search', { params })
    return response.data
  }
}
