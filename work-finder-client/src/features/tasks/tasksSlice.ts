import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import { Task, TaskFilters, TasksState } from './types'
import { TasksResponse } from './api'

const initialState: TasksState = {
  tasks: [],
  loading: false,
  error: null,
  filters: {},
  currentTask: null
}

const tasksSlice = createSlice({
  name: 'tasks',
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload
    },
    setTasks: (state, action: PayloadAction<TasksResponse>) => {
      state.tasks = action.payload.tasks
      state.error = null
    },
    addTask: (state, action: PayloadAction<Task>) => {
      state.tasks.unshift(action.payload)
    },
    updateTask: (state, action: PayloadAction<Task>) => {
      const index = state.tasks.findIndex(task => task.id === action.payload.id)
      if (index !== -1) {
        state.tasks[index] = action.payload
      }
    },
    deleteTask: (state, action: PayloadAction<string>) => {
      state.tasks = state.tasks.filter(task => task.id !== action.payload)
    },
    setCurrentTask: (state, action: PayloadAction<Task | null>) => {
      state.currentTask = action.payload
    },
    setFilters: (state, action: PayloadAction<TaskFilters>) => {
      state.filters = action.payload
    },
    clearFilters: (state) => {
      state.filters = {}
    },
    clearError: (state) => {
      state.error = null
    },
    clearTasks: (state) => {
      state.tasks = []
    }
  }
})

export const {
  setLoading,
  setError,
  setTasks,
  addTask,
  updateTask,
  deleteTask,
  setCurrentTask,
  setFilters,
  clearFilters,
  clearError,
  clearTasks
} = tasksSlice.actions

export default tasksSlice.reducer
