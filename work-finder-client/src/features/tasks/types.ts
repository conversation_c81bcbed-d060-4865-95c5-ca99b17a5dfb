export interface Task {
  id: string
  title: string
  description: string
  status: 'pending' | 'in-progress' | 'completed' | 'cancelled'
  priority: 'low' | 'medium' | 'high'
  dueDate?: string
  assignedTo?: string
  createdBy: string
  createdAt: string
  updatedAt?: string
  tags?: string[]
}

export interface CreateTaskData {
  title: string
  description: string
  status?: 'pending' | 'in-progress' | 'completed' | 'cancelled'
  priority: 'low' | 'medium' | 'high'
  dueDate?: string
  assignedTo?: string
  tags?: string[]
}

export interface UpdateTaskData extends Partial<CreateTaskData> {
  id: string
}

export interface TaskFilters {
  search?: string
  status?: string
  priority?: string
  assignedTo?: string
  dueDate?: string
}

export interface TasksState {
  tasks: Task[]
  loading: boolean
  error: string | null
  filters: TaskFilters
  currentTask: Task | null
}
