import { useState, useCallback, useEffect } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { RootState } from '@/store'
import { tasksApi } from '../api'
import { Task, TaskFilters, CreateTaskData } from '../types'
import { 
  setTasks, 
  setLoading, 
  setError, 
  setFilters,
  addTask,
  updateTask,
  deleteTask 
} from '../tasksSlice'

export function useTasks() {
  const dispatch = useDispatch()
  const { tasks, loading, error, filters } = useSelector((state: RootState) => state.tasks)

  const fetchTasks = useCallback(async (searchFilters?: TaskFilters) => {
    try {
      dispatch(setLoading(true))
      dispatch(setError(null))
      
      const response = await tasksApi.getTasks(searchFilters)
      dispatch(setTasks(response))
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch tasks'
      dispatch(setError(errorMessage))
    } finally {
      dispatch(setLoading(false))
    }
  }, [dispatch])

  const createTask = useCallback(async (taskData: CreateTaskData) => {
    try {
      dispatch(setLoading(true))
      dispatch(setError(null))
      
      const newTask = await tasksApi.createTask(taskData)
      dispatch(addTask(newTask))
      return { success: true, task: newTask }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create task'
      dispatch(setError(errorMessage))
      return { success: false, error: errorMessage }
    } finally {
      dispatch(setLoading(false))
    }
  }, [dispatch])

  const updateTaskById = useCallback(async (id: string, taskData: Partial<CreateTaskData>) => {
    try {
      dispatch(setLoading(true))
      dispatch(setError(null))
      
      const updatedTask = await tasksApi.updateTask({ id, ...taskData })
      dispatch(updateTask(updatedTask))
      return { success: true, task: updatedTask }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update task'
      dispatch(setError(errorMessage))
      return { success: false, error: errorMessage }
    } finally {
      dispatch(setLoading(false))
    }
  }, [dispatch])

  const deleteTaskById = useCallback(async (id: string) => {
    try {
      dispatch(setLoading(true))
      dispatch(setError(null))
      
      await tasksApi.deleteTask(id)
      dispatch(deleteTask(id))
      return { success: true }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete task'
      dispatch(setError(errorMessage))
      return { success: false, error: errorMessage }
    } finally {
      dispatch(setLoading(false))
    }
  }, [dispatch])

  const applyFilters = useCallback((newFilters: TaskFilters) => {
    dispatch(setFilters(newFilters))
    fetchTasks(newFilters)
  }, [dispatch, fetchTasks])

  // Auto-fetch tasks on mount
  useEffect(() => {
    if (tasks.length === 0) {
      fetchTasks(filters)
    }
  }, [])

  return {
    tasks,
    loading,
    error,
    filters,
    fetchTasks,
    createTask,
    updateTask: updateTaskById,
    deleteTask: deleteTaskById,
    applyFilters
  }
}
